# ================================================================================================
# BBB Manager Production Dockerfile
#
# This Dockerfile creates a production-ready container for the BBB Manager system that includes:
# - NestJS application for scheduling and management
# - Ansible automation for BigBlueButton server provisioning/decommissioning
# - All necessary system dependencies for remote server management
# ================================================================================================

# ================================================================================================
# Stage 1: Build Stage - Compile TypeScript and prepare application
# ================================================================================================
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Copy package files for dependency installation
COPY nest-app/package*.json ./nest-app/
COPY package*.json ./

# Install Node.js dependencies
WORKDIR /app/nest-app
RUN npm ci --only=production && \
    npm cache clean --force

# Copy source code
COPY nest-app/ ./

# Build the NestJS application
RUN npm run build

# ================================================================================================
# Stage 2: Production Stage - Runtime environment with Ansible
# ================================================================================================
FROM node:18-alpine AS production

# Metadata
LABEL maintainer="BBB Manager Team"
LABEL description="Production container for BBB Manager with NestJS and Ansible automation"
LABEL version="1.0.0"

# Install system dependencies for Ansible and SSH operations
RUN apk add --no-cache \
    # Python and Ansible dependencies
    python3 \
    py3-pip \
    py3-setuptools \
    py3-wheel \
    py3-cryptography \
    py3-cffi \
    py3-pynacl \
    # SSH and networking tools
    openssh-client \
    sshpass \
    curl \
    wget \
    # System utilities
    bash \
    git \
    ca-certificates \
    tzdata \
    # Build tools for native modules
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    && rm -rf /var/cache/apk/*

# Install Ansible and required collections
RUN pip3 install --no-cache-dir \
    ansible==8.7.0 \
    ansible-core==2.15.8 \
    requests \
    digitalocean \
    && ansible-galaxy collection install \
        community.general \
        community.digitalocean \
        ansible.posix

# Create non-root user for security
RUN addgroup -g 1001 -S bbb-manager && \
    adduser -u 1001 -S bbb-manager -G bbb-manager -h /home/<USER>/bin/bash

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=bbb-manager:bbb-manager /app/nest-app/dist ./nest-app/dist
COPY --from=builder --chown=bbb-manager:bbb-manager /app/nest-app/node_modules ./nest-app/node_modules
COPY --from=builder --chown=bbb-manager:bbb-manager /app/nest-app/package*.json ./nest-app/

# Copy Ansible configuration and playbooks
COPY --chown=bbb-manager:bbb-manager bbb-ansible/ ./bbb-ansible/

# Create necessary directories with proper permissions
RUN mkdir -p \
    /app/logs \
    /app/tmp \
    /home/<USER>/.ssh \
    /home/<USER>/.ansible \
    && chown -R bbb-manager:bbb-manager \
        /app \
        /home/<USER>/.ssh \
        /home/<USER>/.ansible

# Set proper SSH directory permissions
RUN chmod 700 /home/<USER>/.ssh

# Configure Ansible environment
ENV ANSIBLE_HOST_KEY_CHECKING=False \
    ANSIBLE_SSH_RETRIES=3 \
    ANSIBLE_TIMEOUT=30 \
    ANSIBLE_GATHERING=smart \
    ANSIBLE_STDOUT_CALLBACK=yaml \
    ANSIBLE_ROLES_PATH=/app/bbb-ansible/roles \
    ANSIBLE_INVENTORY=/app/bbb-ansible/inventory \
    ANSIBLE_CONFIG=/app/bbb-ansible/ansible.cfg

# Configure Node.js environment
ENV NODE_ENV=production \
    PORT=3000 \
    TZ=UTC

# Switch to non-root user
USER bbb-manager

# Set working directory for the application
WORKDIR /app/nest-app

# Expose application port
EXPOSE 3000

# Start the application
CMD ["node", "dist/main.js"]
