# BBB Manager Azure Deployment Guide

This guide provides instructions for deploying the BBB Manager system on Azure using Azure Container Instances (ACI) or Azure Container Apps.

## 🏗️ Architecture Overview

The BBB Manager consists of two main components:

1. **BBB Manager Application** - NestJS application with integrated Ansible automation
2. **Redis Cache** - For caching lecture data and system state

## 📋 Prerequisites

### Azure Requirements
- **Azure Subscription** with Container Instances or Container Apps enabled
- **Azure Container Registry** (ACR) for storing the Docker image
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **CPU**: Minimum 2 vCPUs (4+ recommended)
- **Network**: Outbound internet access for DigitalOcean API and server management

### Required Accounts & Access
- DigitalOcean account with API token
- Hasura GraphQL endpoint and admin secret
- SSH access to Scalelite load balancer
- SSH access to processing server
- DigitalOcean Spaces credentials

## 🚀 Azure Deployment Steps

### 1. Prepare the Application

```bash
# Clone the repository
git clone <repository-url>
cd bbb-manager
```

### 2. Build and Push to Azure Container Registry

```bash
# Build the Docker image
docker build -t bbb-manager:latest .

# Tag for Azure Container Registry
docker tag bbb-manager:latest <your-acr-name>.azurecr.io/bbb-manager:latest

# Push to ACR
docker push <your-acr-name>.azurecr.io/bbb-manager:latest
```

### 3. Environment Configuration

Prepare your environment variables for Azure deployment:

```env
# ================================
# APPLICATION SETTINGS
# ================================
NODE_ENV=production
PORT=3000

# ================================
# DIGITALOCEAN CONFIGURATION
# ================================
DIGITALOCEAN_API_TOKEN=your_digitalocean_api_token_here
DIGITALOCEAN_BASE_DOMAIN=geerd.net

# ================================
# HASURA GRAPHQL DATABASE
# ================================
HASURA_GRAPHQL_ENDPOINT=https://your-hasura-endpoint.hasura.app/v1/graphql
HASURA_GRAPHQL_ADMIN_SECRET=your_hasura_admin_secret

# ================================
# REDIS CACHE CONFIGURATION
# ================================
REDIS_HOST=redis
REDIS_PORT=6379

# ================================
# SCALELITE LOAD BALANCER
# ================================
SCALELITE_SERVER_IP=**************
SCALELITE_SSH_PASSWORD=your_scalelite_ssh_password

# ================================
# PROCESSING SERVER
# ================================
PROCESSING_SERVER_IP=************
PROCESSING_SERVER_PASSWORD=your_processing_server_password

# ================================
# DIGITALOCEAN SPACES
# ================================
SPACES_BUCKET_NAME=bbb-terraform
SPACES_REGION=fra1
SPACES_ACCESS_KEY=your_spaces_access_key
SPACES_SECRET_KEY=your_spaces_secret_key

# ================================
# DEPLOYMENT PATHS (Optional)
# ================================
DATA_PATH=./data
LOGS_PATH=./logs
TMP_PATH=./tmp
SSH_KEYS_PATH=~/.ssh
```

### 4. Deploy to Azure Container Instances

#### Option A: Using Azure Portal

1. **Create Container Instance**:
   - Go to Azure Portal → Container Instances → Create
   - Select your subscription and resource group
   - Set container name: `bbb-manager`
   - Set image source: Azure Container Registry
   - Select your ACR and image: `bbb-manager:latest`

2. **Configure Resources**:
   - CPU: 2 cores
   - Memory: 4 GB
   - OS type: Linux

3. **Set Environment Variables**:
   Add all required environment variables from the list below

4. **Configure Networking**:
   - DNS name label: `your-bbb-manager`
   - Port: 3000
   - Protocol: TCP

#### Option B: Using Azure Container Apps

1. **Create Container App Environment**
2. **Create Container App**:
   - Use your ACR image
   - Set CPU: 2.0, Memory: 4Gi
   - Configure environment variables
   - Set ingress to external with port 3000

## 🔧 Configuration Details

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `DIGITALOCEAN_API_TOKEN` | ✅ | - | DigitalOcean API token for droplet management |
| `DIGITALOCEAN_BASE_DOMAIN` | ✅ | `geerd.net` | Base domain for BBB servers |
| `HASURA_GRAPHQL_ENDPOINT` | ✅ | - | Hasura GraphQL endpoint URL |
| `HASURA_GRAPHQL_ADMIN_SECRET` | ✅ | - | Hasura admin secret |
| `SCALELITE_SERVER_IP` | ✅ | `**************` | Scalelite load balancer IP |
| `SCALELITE_SSH_PASSWORD` | ✅ | - | SSH password for Scalelite server |
| `PROCESSING_SERVER_IP` | ✅ | `************` | Processing server IP |
| `PROCESSING_SERVER_PASSWORD` | ✅ | - | SSH password for processing server |
| `SPACES_BUCKET_NAME` | ✅ | `bbb-terraform` | DigitalOcean Spaces bucket name |
| `SPACES_REGION` | ✅ | `fra1` | DigitalOcean Spaces region |
| `SPACES_ACCESS_KEY` | ✅ | - | DigitalOcean Spaces access key |
| `SPACES_SECRET_KEY` | ✅ | - | DigitalOcean Spaces secret key |

### SSH Key Configuration for Azure

Since Azure Container Instances don't support volume mounts for SSH keys, you have two options:

1. **Build SSH keys into the image** (less secure, for development only)
2. **Use Azure Key Vault** (recommended for production):
   - Store SSH private key in Azure Key Vault
   - Mount as secret in Container Instance
   - Configure container to read from mounted secret path

## 🔍 Monitoring and Troubleshooting

### Check Service Status in Azure

```bash
# View container logs in Azure Portal
# Go to Container Instances → Your Container → Logs

# Or use Azure CLI
az container logs --resource-group <resource-group> --name bbb-manager

# Check container status
az container show --resource-group <resource-group> --name bbb-manager
```

### Common Issues

1. **SSH Connection Issues**
   - Verify SSH keys are mounted correctly
   - Check SSH key permissions (600 for private key)
   - Ensure target servers are accessible

2. **Environment Variable Issues**
   - Verify all required variables are set in `.env`
   - Check for typos in variable names
   - Ensure no trailing spaces in values

3. **Ansible Execution Issues**
   - Check Ansible logs in Azure Portal container logs
   - Verify SSH connectivity from container to target servers
   - Ensure environment variables are properly set

### Performance Tuning in Azure

- **Scale up**: Increase CPU/Memory in Container Instance settings
- **Monitor**: Use Azure Monitor to track resource usage
- **Redis**: Consider using Azure Cache for Redis for better performance

## 🔒 Security Considerations

1. **Environment Variables**: Store sensitive data in `.env` file, never commit to version control
2. **SSH Keys**: Use read-only mounts and proper file permissions
3. **Network**: Use internal Docker networks for service communication
4. **Updates**: Regularly update base images and dependencies
5. **Monitoring**: Implement log monitoring and alerting

## 🔄 Updates and Maintenance

### Update Application in Azure

```bash
# Build new image
docker build -t bbb-manager:latest .
docker tag bbb-manager:latest <your-acr-name>.azurecr.io/bbb-manager:latest
docker push <your-acr-name>.azurecr.io/bbb-manager:latest

# Update container instance in Azure Portal:
# 1. Go to Container Instances → Your Container
# 2. Stop the container
# 3. Update the image tag or restart with new image
# 4. Start the container
```

### Backup and Monitoring

- **Logs**: Use Azure Monitor and Log Analytics for centralized logging
- **Metrics**: Monitor CPU, memory, and network usage through Azure Portal
- **Alerts**: Set up alerts for container failures or resource thresholds

## 📞 Support

For issues and questions:
1. Check the logs first
2. Verify environment configuration
3. Test SSH connectivity manually
4. Review Ansible playbook execution
